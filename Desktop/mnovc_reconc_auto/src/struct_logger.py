import os
import inspect
import sys
import logging
from datetime import datetime
from logging.handlers import MemoryHandler
import structlog


def generate_job_id():
    return datetime.today().strftime("%Y%m%d_%H%M%S")


def reorder_fields(event_dict):
    ordered = {}
    for key in ["asctime", "levelname", "name", "message", "job_id"]:
        if key in event_dict:
            ordered[key] = event_dict.pop(key)
        elif key == "job_id":
            # Ensure job_id is always present, even if null
            ordered[key] = None
    # Add any remaining fields
    ordered.update(event_dict)
    return ordered


def rename_fields(logger, method_name, event_dict):


    event_dict["asctime"] = event_dict.pop("timestamp", None)
    level = event_dict.pop("level", None)
    if level:
        event_dict["levelname"] = level.upper()
    event_dict["message"] = event_dict.pop("event", None)

    # Get the actual filename where the logging is happening
    if hasattr(logger, 'name'):
        logger_name = logger.name
        # If the logger name is from our custom module or structlog, dynamically find the calling file
        #if (logger_name in ['struct_logger', 'structlogtesing', 'logger', '__main__'] or
        if (logger_name in ['logger'] or
            'struct_logger' in logger_name or 'structlog' in logger_name):
            try:
                # Walk up the call stack to find the actual calling file
                frame = inspect.currentframe()
                calling_filename = "main.py"  # fallback

                # Go deeper in the stack to find the real caller
                stack_depth = 0
                while frame and stack_depth < 20:  # Limit depth to avoid infinite loops
                    frame = frame.f_back
                    stack_depth += 1

                    if frame and frame.f_code.co_filename:
                        filename = os.path.basename(frame.f_code.co_filename)
                        filepath = frame.f_code.co_filename

                        # Skip internal Python/structlog files but allow our custom files
                        if (filename.endswith('.py') and
                            not filename.startswith(('structlog', 'logging', '_')) and
                            filename not in ['<stdin>', '<string>', 'threading.py', 'queue.py', 'stdlib.py'] and
                            'site-packages' not in filepath and
                            '/structlog/' not in filepath and
                            '/logging/' not in filepath and
                            # Allow our custom files in the project directory
                            ('tmo_Reconciliation' in filepath or 'Desktop' in filepath)):
                            calling_filename = filename
                            break

                logger_name = calling_filename
            except Exception:
                # Fallback to main.py if inspection fails
                logger_name = "main.py"
        event_dict["name"] = logger_name
    elif "logger" in event_dict:
        event_dict["name"] = event_dict.pop("logger")
    else:
        event_dict["name"] = "root"

    # Remove redundant logger field since we already have name
    event_dict.pop("logger", None)
    event_dict.pop("positional_args", None)
    # Ensure job_id is preserved in the output
    return reorder_fields(event_dict)


def setup_structlog(buffered=True, unique_run_id=None):
    if unique_run_id is None:
        unique_run_id = generate_job_id()

    log_dir_base = os.getenv("LOGGER_DIR")
    current_date = datetime.today().strftime('%Y-%m-%d')
    log_directory = os.path.join(log_dir_base, current_date)
    os.makedirs(log_directory, exist_ok=True)

    log_file = os.path.join(log_directory, f'application_{unique_run_id}.json')

    processor_formatter = structlog.stdlib.ProcessorFormatter(
        processor=structlog.processors.JSONRenderer(),
        foreign_pre_chain=[
            structlog.stdlib.add_log_level,
            structlog.stdlib.add_logger_name,  # This will preserve logger names from standard logging
            structlog.processors.TimeStamper(fmt="%Y-%m-%d %H:%M:%S,%f", utc=False),
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.format_exc_info,
            rename_fields,
        ],
    )

    file_handler = logging.FileHandler(log_file, mode='a', encoding='utf-8')
    file_handler.setLevel(logging.INFO)
    file_handler.setFormatter(processor_formatter)

    if buffered:
        memory_handler = MemoryHandler(
            capacity=1000,
            flushLevel=logging.ERROR,
            target=file_handler
        )
        active_handler = memory_handler
    else:
        active_handler = file_handler

    root_logger = logging.getLogger()
    root_logger.setLevel(logging.INFO)
    root_logger.handlers = []
    root_logger.addHandler(active_handler)

    structlog.configure(
        processors=[
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,  # Add logger name to all logs
            structlog.processors.TimeStamper(fmt="%Y-%m-%d %H:%M:%S,%f", utc=False),
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.format_exc_info,
            rename_fields,
            structlog.stdlib.ProcessorFormatter.wrap_for_formatter,
        ],
        context_class=dict,
        logger_factory=structlog.stdlib.LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )

    # Bind the job_id to the logger context
    structlog.contextvars.bind_contextvars(job_id=unique_run_id)
    logger = structlog.get_logger()
    # Bind job_id directly to the logger instance
    logger = logger.bind(job_id=unique_run_id)

    return logger, active_handler


logger = None

def log_uncaught_exceptions(exc_type, exc_value, exc_traceback):
    if issubclass(exc_type, KeyboardInterrupt):
        sys.__excepthook__(exc_type, exc_value, exc_traceback)
        return
    if logger:
        logger.exception("UNCAUGHT_EXCEPTION", exc_info=(exc_type, exc_value, exc_traceback))
    else:
        # Use the standard exception hook to show full traceback
        import traceback
        traceback.print_exception(exc_type, exc_value, exc_traceback)
        logging.error("Unhandled exception occurred - see traceback above")
    

sys.excepthook = log_uncaught_exceptions
