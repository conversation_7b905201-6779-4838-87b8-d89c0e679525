#!/usr/bin/env python3
"""Test that writes output to a file"""

import sys
import traceback

# Redirect output to a file
with open('test_output.txt', 'w') as f:
    f.write("Starting test...\n")
    f.flush()
    
    try:
        # Import struct_logger to activate custom exception handler
        import struct_logger
        f.write("struct_logger imported successfully\n")
        f.flush()
    except Exception as e:
        f.write(f"Failed to import struct_logger: {e}\n")
        f.flush()
        sys.exit(1)
    
    f.write("About to cause an error...\n")
    f.flush()

# This will cause an uncaught exception that should be handled by struct_logger
result = "test" + None  # This line will cause TypeError
