#!/usr/bin/env python3
"""
Test script to verify that the improved exception handling in struct_logger.py
shows proper tracebacks with line numbers for uncaught exceptions.
"""

import os
import sys

# Import struct_logger to activate the custom exception handler
try:
    from struct_logger import *
    print("struct_logger imported successfully")
except Exception as e:
    print(f"Failed to import struct_logger: {e}")
    sys.exit(1)

def function_that_causes_error():
    """This function will cause a deliberate error to test exception handling."""
    # This will cause a TypeError - trying to add string and None
    result = "test" + None  # Line that will cause the error
    return result

def another_function():
    """Another function to show nested call stack."""
    print("Calling function_that_causes_error...")
    return function_that_causes_error()

def test_path_error():
    """Test a path-related error similar to what you might see in your main script."""
    # This will cause the same type of error as in your main script
    bad_path = None
    os.makedirs(bad_path, exist_ok=True)  # This line will cause the error

def main():
    print("Testing exception handling improvements...")
    print("=" * 50)
    
    # Test 1: Basic TypeError
    print("Test 1: Testing TypeError with nested function calls...")
    try:
        another_function()
    except Exception as e:
        print(f"Caught in try-except: {e}")
        return
    
    # This should not be reached if the exception is caught
    print("Test 1 completed without exception (unexpected)")
    
    # Test 2: Path error (similar to your main script issue)
    print("\nTest 2: Testing path error (os.makedirs with None)...")
    test_path_error()  # This will cause an uncaught exception

if __name__ == "__main__":
    main()
