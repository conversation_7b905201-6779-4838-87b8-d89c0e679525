#!/usr/bin/env python3
"""Simple test to check exception handling"""

print("Starting simple test...")

# Import struct_logger to activate custom exception handler
import struct_logger

print("struct_logger imported")

# This will cause an uncaught exception
def cause_error():
    print("About to cause an error...")
    result = "test" + None  # This line will cause TypeError
    return result

print("Calling function that will cause error...")
cause_error()
print("This line should not be reached")
