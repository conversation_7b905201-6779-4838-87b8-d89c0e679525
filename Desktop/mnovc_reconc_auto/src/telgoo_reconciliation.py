#### VERSION 1.0 
#### Company : MVNOC
#### File Name : telgoo_reconciliation.py
#### File Location : mvnoc-prod-debian-billingautomation  
#### PURPOSE : To reconsile telgoo invoice and generate reconciliation reports for Iot and Non-Iot users.
#### Version      Date           Developer Name                     Remarks
#### 1.0       25-06-2025           Vyomakesh                    Initial Version


import pandas as pd
import paramiko.sftp_client
import numpy as np
from datetime import date, datetime, timedelta
from dateutil.relativedelta import relativedelta
from struct_logger import *
from dotenv import load_dotenv
import boto3
from concurrent.futures import ThreadPoolExecutor, as_completed
import snowflake.connector
import paramiko
import zipfile


load_dotenv()

# Constants for filtering
TENANT_IDS = ['8', '1023', '1010', '1009']
IOT_BILL_CODES = ['61100017', '82200022', '81800040', '81600011']

try:
    aws_access_key_id=os.getenv('AWS_ACCESS_KEY_ID')
    aws_secret_access_key=os.getenv('AWS_SECRET_ACCESS_KEY')
    Bucket=os.getenv('AWS_BUCKET_NAME')
except Exception as e:
    logger.exception("Error loading aws variables: %s", e)


def iot_recon(IOT_SNAPSHOT_BILLABLE, telgoo_iot):
    """
    Perform IoT reconciliation:
      1. Load MVNO and Telgoo billing data.
      2. Calculate active subscriber counts per date and per WPS.
      3. Filter IoT-specific records and reconcile against Telgoo.
      4. Save reconciliation reports to CSV files.
    """

    # 3) Filter IoT data
    try:
        telgoo_billable_days = telgoo_iot['BILLABLE_DAYS'].sum()
        logger.info("Filtered Telgoo IoT billable records: %d rows", telgoo_billable_days)
    except Exception as e:
        logger.exception("Error filtering IoT data: %s", e)

    #sanpshot data using 
    try:
        telgoo_iot['SUBSCRIBER_ID'] = telgoo_iot['SUBSCRIBER_ID'].astype(np.int64)
        IOT_SNAPSHOT_BILLABLE['SUBSCRIBER_ID'] = IOT_SNAPSHOT_BILLABLE['SUBSCRIBER_ID'].astype(np.int64)
        MSISDNWISE_iot=pd.merge(telgoo_iot,IOT_SNAPSHOT_BILLABLE,on = 'SUBSCRIBER_ID',how='left')
        #assign a name to the billable days column
        mvnoc_billable_days = MSISDNWISE_iot['ACTIVE_DAYS'].sum()
        logger.info("Filtered snapdhot IoT billable records: %d rows", mvnoc_billable_days)
    except Exception as e:
        logger.exception("Error filtering snapshot IoT data: %s", e)

    return MSISDNWISE_iot, mvnoc_billable_days, telgoo_billable_days

   

def non_iot_recon(non_iot_MSISDN, telgoo_non_iot):
    
    #Calcualtion of Non-IOT users
    try:
        telgoo_noniot_users = len(telgoo_non_iot['SUBSCRIBER_ID'])
        logger.info("Calculated Telgoo non-IoT users: %d", telgoo_noniot_users)
    except Exception as e:
        logger.exception("Error calculating Telgoo non-IoT users: %s", e)
         
    try:
        telgoo_non_iot['SUBSCRIBER_ID'] = telgoo_non_iot['SUBSCRIBER_ID'].astype(int)
        non_iot_MSISDN['SUBSCRIBER_ID'] = non_iot_MSISDN['SUBSCRIBER_ID'].astype(int)
        MSISDNWISE_non_iot=pd.merge(telgoo_non_iot,non_iot_MSISDN,on = 'SUBSCRIBER_ID',how='left')
        mvnoc_noniot_users = len(MSISDNWISE_non_iot[~((MSISDNWISE_non_iot['SUBSCRIBER_STATE'] == 'ACTIVE') &(MSISDNWISE_non_iot['ACTIVE_DAYS'].isnull()))]['ACTIVE_DAYS'])
  
    except Exception as e:
        logger.exception("Error filtering snapshot non-IoT data: %s", e)
    return telgoo_noniot_users, MSISDNWISE_non_iot, mvnoc_noniot_users

  

def compute_window(today: date = None):
    """
    Compute:
      start_date = 2nd day of the month *before* `today.month`
      end_date   = 1st day of the month after start_date (i.e. the 1st of current month)

    Example:
      today = date(2024, 7, 25)
      -> start_date = date(2024, 6, 2)
      -> end_date   = date(2024, 7, 1)
    """
    if today is None:
        today = date.today()

    # 1) get first day of the current month
    first_of_curr = today.replace(day=1)

    # 2) first day of previous month
    first_of_prev = first_of_curr - relativedelta(months=1)

    # 3) start_date = 2nd day of the previous month
    start_date = first_of_prev.replace(day=2)

    # 4) end_date = first_of_curr (1st of current month)
    end_date = first_of_curr

    return start_date, end_date


def make_non_iot_results_excel(MSISDNWISE: pd.DataFrame, mvnoc_billable: int,telgoo_billable: int, month_name: str, year: str):
    """
    Creates an Excel file with two sheets:
    1. Summary sheet: cells formatted and formulas applied as specified.
    2. Data sheet: contents of the provided DataFrame.
    
    Parameters:
    - df: DataFrame to write to 'data' sheet.
    - output_dir: Directory where the Excel file will be saved.
    - telgoo_total: Total Users count for Telgoo.
    - mvnoc_total: Total Users count for MVNOC.
    - filename: Name of the resulting Excel file.
    
    Returns the full path to the created Excel file.
    """
    filename = f"Telgoo_reconciliation_NonIoT_{month_name}{year}.xlsx"
    output_dir = os.getenv("REPORT_DIR")
    
    # Ensure output directory exists
    os.makedirs(output_dir, exist_ok=True)
    file_path = os.path.join(output_dir, filename)
    
    # Use XlsxWriter engine for formula support
    with pd.ExcelWriter(file_path, engine='xlsxwriter') as writer:
        # 1. Summary sheet
        wb  = writer.book
        ws = wb.add_worksheet("summary sheet")
        writer.sheets["summary sheet"] = ws
        
        # Write labels
        labels = [("A3", "Total Users"),
                  ("A4", "First @ 25,000 Users"),
                  ("A5", "25000 + Users"),
                  ("A6", "Total Cost"),
                  ("B2", "Telgoo"),
                  ("C2", "MVNOC"),
                  ("D2", "Difference")]
        for cell, text in labels:
            ws.write(cell, text)
        try:
            # Write totals into row3
            ws.write("B3", telgoo_billable )
            ws.write("C3", mvnoc_billable)
            logger.info("Toatl Users added succesfully")
        except Exception as e:
            logger.exception("Error writing total Users into row3: %s", e)
        
        # Formulas for row4 (0.25 factor)
        ws.write_formula("B4", f"=IF(B3>25000, B3*0.25, 0)")
        ws.write_formula("C4", f"=IF(C3>25000, C3*0.25, 0)")
        
        # Formulas for row5 (0.24 factor)
        ws.write_formula("B5", f"=IF(B3>25000, (B3-25000)*0.24, 0)")
        ws.write_formula("C5", f"=IF(C3>25000, (C3-25000)*0.24, 0)")

        # Formual for row6 total cost calculation
        ws.write_formula("B6", f"=B4+B5")
        ws.write_formula("C6", f"=C4+C5")

        # Difference columns
        ws.write_formula("D3", "=B3-C3")
        ws.write_formula("D4", "=B4-C4")
        ws.write_formula("D5", "=B5-C5")
        ws.write_formula("D6", "=B6-C6")
        
        # 2. Data sheet

        MSISDNWISE.to_excel(writer, sheet_name="MSISDNWISE", index=False)
        
        logger.info("Written summary and data sheets")
    
    logger.info(f"Excel file saved to {file_path}")
    return file_path

def make_iot_results_excel(MSISDNWISE: pd.DataFrame, mvnoc_billable: int,telgoo_billable: int, month_name: str, year: str ):
    """
    Creates an Excel file with two sheets:
    1. Summary sheet: cells formatted and formulas applied as specified.
    2. Data sheet: contents of the provided DataFrame.
    
    Parameters:
    - df: DataFrame to write to 'data' sheet.
    - output_dir: Directory where the Excel file will be saved.
    - telgoo_total: Total Users count for Telgoo.
    - mvnoc_total: Total Users count for MVNOC.
    - filename: Name of the resulting Excel file.
    
    Returns the full path to the created Excel file.
    """
    #report_date=start_date.strptime("%Y-%m-%d")

    filename = f"Telgoo_reconciliation_IoT_{month_name}{year}.xlsx"
    output_dir = os.getenv("REPORT_DIR")
    
    # Ensure output directory exists
    os.makedirs(output_dir, exist_ok=True)
    file_path = os.path.join(output_dir, filename)
    
    # Use XlsxWriter engine for formula support
    with pd.ExcelWriter(file_path, engine='xlsxwriter') as writer:
        # 1. Summary sheet
        wb  = writer.book
        ws = wb.add_worksheet("summary sheet")
        writer.sheets["summary sheet"] = ws
        
        # Write labels
        labels = [("A4", "Total Active days"),
                  ("A5", "Cost"),
                  ("B3", "Telgoo"),
                  ("C3", "MVNOC"),
                  ("D3", "Difference")]
        for cell, text in labels:
            ws.write(cell, text)
        try:
            # Write totals into row3
            ws.write("B4", telgoo_billable )
            ws.write("C4", mvnoc_billable)
            logger.info("Toatl Users added succesfully")
        except Exception as e:
            logger.exception("Error writing total Users into row3: %s", e)
        
        # Formulas for row4 (0.25 factor)
        ws.write_formula("B5", f"=B4*0.00806")
        ws.write_formula("C5", f"=C4*0.00806")
        
        # Difference columns
        ws.write_formula("D4", "=B4-C4")
        ws.write_formula("D5", "=B5-C5")
        
        # 2. Data sheet

        MSISDNWISE.to_excel(writer, sheet_name="MSISDNWISE", index=False)
        
        logger.info("Written summary and data sheets")
    
    logger.info(f"Excel file saved to {file_path}")
    return file_path

def main():
    # Generate unique run ID and setup structlog
    run_id = generate_job_id()
    logger, handler = setup_structlog(unique_run_id=run_id, buffered=True)
    
    try:
        start_time = datetime.today()
        logger.info("Telgoo Recon job started at %s.", start_time.strftime('%Y-%m-%d %H:%M:%S'))
    except Exception as e:
        logger.exception("Error logging start time: %s", e)

    try:
        today = datetime.today().date()
        today_usage_date = today  - timedelta(days=1)
        today_file_date = today_usage_date + timedelta(days=1)
        logger.info("Dates computed: Today=%s, Usage Date=%s, File Date=%s", today, today_usage_date, today_file_date)
    except Exception as e:
        logger.exception("Error computing dates: %s", e)
    
    try :
        #start_date, end_date = compute_window()
        start_date = datetime(2025, 5, 2).strftime('%Y-%m-%d')
        end_date = datetime(2025, 6, 1).strftime('%Y-%m-%d')
        report_date = datetime.strptime(start_date, "%Y-%m-%d")
        month_name=report_date.strftime("%B").upper()
        month_name = month_name[:3]
        year = report_date.strftime("%Y")
        #date_list = [(start_date + timedelta(days=i)).strftime('%Y%m%d') for i in range((end_date - start_date).days + 1)]
        logger.info("Dates computed: Start Date=%s, End Date=%s", start_date, end_date)
    except Exception as e:
        logger.exception("Error computing dates: %s", e)



    #SFTP connection
    try:
        hostname = os.getenv('SFTP_HOSTNAME')
        port     = int(os.getenv('SFTP_PORT'))
        username = os.getenv('SFTP_USERNAME')
        password = os.getenv('SFTP_PASSWORD')
        logger.info("SFTP credentials loaded successfully.")

    except Exception as e:
        logger.exception("Error loading SFTP credentials: %s", e)
    try:
        transport = paramiko.Transport((hostname, port))
        transport.connect(username=username, password=password)

        # Create an SFTP session
        sftp = paramiko.SFTPClient.from_transport(transport)
        logger.info("SFTP connection established successfully.")
    except Exception as e:
        logger.exception("Error connecting to SFTP: %s", e)
    try:
        # sftp = ssh.open_sftp()
        logger.info("SFTP file transfer client created successfully.")
    except Exception as e:
        logger.exception("Error creating SFTP file transfer client: %s", e)
    try:
        dir_path = os.getenv('SFTP_DIRECTORY')
        sftp.chdir(dir_path)
        logger.info("Changed directory to %s", dir_path)
    except Exception as e:
        logger.exception("Error changing directory: %s", e)
    try:
        remote_file = f'MVNO_Connect_BIlling_{month_name}{year}.zip'
        local_file  = f'ZIP/{remote_file}'
        sftp.get(remote_file, local_file)
        logger.info("Zip File downloaded successfully.")
    except Exception as e:
        logger.exception("Error the downloading Zip file: %s", e)
    try:
        with zipfile.ZipFile(local_file, 'r') as zip_ref:
            zip_ref.extractall('ZIP/')
        logger.info("Zip file extracted successfully.")
    except Exception as e:
        logger.exception("Error extracting Zip file: %s", e)
    try:
        sftp.close()
        # ssh.close()
        logger.info("SFTP connection closed successfully.")
    except Exception as e:
        logger.exception("Error closing SFTP connection: %s", e)

 # 1) Load data
    try:
        telgoo_iot_file = os.getenv("TELGOO_IOT_CSV_FILE") + str(month_name)+ str(year) +".csv"
        telgoo_non_iot_file = os.getenv("TELGOO_NON_IOT_CSV_FILE") + str(month_name)+ str(year) +".csv"
        telgoo_iot = pd.read_csv(telgoo_iot_file, dtype={'SUBSCRIBER_ID': str})
        telgoo_non_iot = pd.read_csv(telgoo_non_iot_file, dtype={'SUBSCRIBER_ID': str})
        logger.info("Loaded data: MVNO (%s) and Telgoo (%s)", telgoo_iot, telgoo_non_iot)
    except Exception as e:
        logger.exception("Error loading input files: %s", e)

    try:
        # Set up Snowflake connection using environment variables
        connection = snowflake.connector.connect(
            user=os.getenv('SNOWFLAKE_USER'),
            password=os.getenv('SNOWFLAKE_PASSWORD'),
            account=os.getenv('SNOWFLAKE_ACCOUNT'),
            warehouse=os.getenv('SNOWFLAKE_WAREHOUSE'),
            database=os.getenv('SNOWFLAKE_DATABASE'),
            schema=os.getenv('SNOWFLAKE_SCHEMA'),
            role=os.getenv('SNOWFLAKE_ROLE')
        )
        if connection:
            logger.info("Successfully connected to Snowflake.")
            cursor = connection.cursor()
        else:
            logger.error("Snowflake connection was not established.")  
    except Exception as e:
        logger.exception("Error connecting to Snowflake: %s", e)

    # --- Retrieve IOT data ---
    try:
        query = 'CALL "iot_activedays_retrieve"(%s,%s)'                    
        cursor.execute(query, (start_date, end_date))
        cols = [desc[0] for desc in cursor.description]
        data = cursor.fetchall()
        if not data:
            logger.warning("No IOT Snapshot data found for dates %s to %s", start_date, end_date)
            IOT_SNAPSHOT_BILLABLE = pd.DataFrame(columns=cols)
        else:
            IOT_SNAPSHOT_BILLABLE = pd.DataFrame(data, columns=cols)
            logger.info("Retrieved IOT Snapshot data for dates %s to %s. Total rows: %d  ", start_date, end_date, len(IOT_SNAPSHOT_BILLABLE))
    except Exception as e:
        logger.exception("Error retrieving IOT Snapshot data for dates %s to %s: %s", start_date, end_date, e)
    
    # --- Retrieve NON-IOT data  ---
    try:
        query = 'CALL "noniot_activedays_retrieve"(%s,%s)'                    
        cursor.execute(query, (start_date, end_date))
        cols = [desc[0] for desc in cursor.description]
        data = cursor.fetchall()
        if not data:
            logger.warning("No NON-IOT Snapshot data found for dates %s to %s", start_date, end_date)
            non_iot_MSISDN = pd.DataFrame(columns=cols)
        else:
            non_iot_MSISDN = pd.DataFrame(data, columns=cols)
            logger.info("Retrieved NON-IOT Snapshot data for dates %s to %s. Total rows: %d  ", start_date, end_date, len(non_iot_MSISDN))
    except Exception as e:
        logger.exception("Error retrieving NON-IOT Snapshot data for dates %s to %s: %s", start_date, end_date, e)
        

    try:
        telgoo_noniot_users, MSISDNWISE_non_iot, mvnoc_noniot_users= non_iot_recon(non_iot_MSISDN, telgoo_non_iot)
    except Exception as e:
        logger.exception("Error in Non-IoT Reconciliation': %s", e)
        telgoo_noniot_users =0 
        MSISDNWISE_non_iot = pd.DataFrame()
        mvnoc_noniot_users=0
    
    try:
        report_path = make_non_iot_results_excel(
        MSISDNWISE_non_iot,
        mvnoc_noniot_users,
        telgoo_noniot_users,
        month_name,
        year
        )
        logger.info("Non-Iot Reconciliation made successfully.")
    except Exception as e:
        logger.exception("Error in Non-Iot Reconciliation report genration.': %s", e)

    try:
        MSISDNWISE_iot, mvnoc_billable_days, telgoo_billable_days= iot_recon(IOT_SNAPSHOT_BILLABLE, telgoo_iot)
        logger.info("Iot Reconciliation made successfully.")
    except Exception as e:
        logger.exception("Error in Iot Reconciliation': %s", e)
        MSISDNWISE_iot= pd.DataFrame()
        mvnoc_billable_days=0
        telgoo_billable_days=0

    try:
        report_path = make_iot_results_excel(
        MSISDNWISE_iot,
        mvnoc_billable_days,
        telgoo_billable_days,
        month_name,
        year
        )
        logger.info("Iot Reconciliation report generated successfully.")
    except Exception as e:
        logger.exception("Error in Iot Reconciliation report genration.': %s", e)


    try:
        if cursor:
            cursor.close()
        if connection:
            connection.close()
        logger.info("Snowflake connection is closed.")
    except Exception as close_ex:
        logger.exception("Error while closing Snowflake connection: %s", close_ex)

    try:
        end_time = datetime.today()
        logger.info("Telgoo reconciliation job ended at %s.", end_time.strftime('%Y-%m-%d %H:%M:%S'))
    except Exception as e:
        logger.exception("Error logging end time: %s", e)
        
        handler.flush()

if __name__ == "__main__":
    main()
