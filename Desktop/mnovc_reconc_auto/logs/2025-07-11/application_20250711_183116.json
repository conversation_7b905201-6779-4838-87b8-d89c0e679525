{"asctime": "2025-07-11 18:31:16,228635", "levelname": "INFO", "name": "telgoo_reconciliation.py", "message": "Telgoo Recon job started at 2025-07-11 18:31:16.", "job_id": "20250711_183116"}
{"asctime": "2025-07-11 18:31:16,228755", "levelname": "INFO", "name": "telgoo_reconciliation.py", "message": "Dates computed: Today=2025-07-11, Usage Date=2025-07-10, File Date=2025-07-11", "job_id": "20250711_183116"}
{"asctime": "2025-07-11 18:31:16,229060", "levelname": "INFO", "name": "telgoo_reconciliation.py", "message": "Dates computed: Start Date=2025-06-02, End Date=2025-07-01", "job_id": "20250711_183116"}
{"asctime": "2025-07-11 18:31:16,229108", "levelname": "INFO", "name": "telgoo_reconciliation.py", "message": "SFTP credentials loaded successfully.", "job_id": "20250711_183116"}
{"asctime": "2025-07-11 18:32:31,243105", "levelname": "ERROR", "name": "telgoo_reconciliation.py", "message": "Error connecting to SFTP: Unable to connect to ************: [Errno 60] Operation timed out", "job_id": "20250711_183116", "exception": "Traceback (most recent call last):\n  File \"/Users/<USER>/Desktop/mnovc_reconc_auto/src/telgoo_reconciliation.py\", line 317, in main\n    transport = paramiko.Transport((hostname, port))\n                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/Desktop/mnovc_reconc_auto/.venv/lib/python3.12/site-packages/paramiko/transport.py\", line 499, in __init__\n    raise SSHException(\nparamiko.ssh_exception.SSHException: Unable to connect to ************: [Errno 60] Operation timed out"}
{"asctime": "2025-07-11 18:32:31,262378", "levelname": "INFO", "name": "telgoo_reconciliation.py", "message": "SFTP file transfer client created successfully.", "job_id": "20250711_183116"}
{"asctime": "2025-07-11 18:32:31,262526", "levelname": "ERROR", "name": "telgoo_reconciliation.py", "message": "Error changing directory: cannot access local variable 'sftp' where it is not associated with a value", "job_id": "20250711_183116", "exception": "Traceback (most recent call last):\n  File \"/Users/<USER>/Desktop/mnovc_reconc_auto/src/telgoo_reconciliation.py\", line 332, in main\n    sftp.chdir(dir_path)\n    ^^^^\nUnboundLocalError: cannot access local variable 'sftp' where it is not associated with a value"}
{"asctime": "2025-07-11 18:32:31,263001", "levelname": "ERROR", "name": "telgoo_reconciliation.py", "message": "Error the downloading Zip file: cannot access local variable 'sftp' where it is not associated with a value", "job_id": "20250711_183116", "exception": "Traceback (most recent call last):\n  File \"/Users/<USER>/Desktop/mnovc_reconc_auto/src/telgoo_reconciliation.py\", line 339, in main\n    sftp.get(remote_file, local_file)\n    ^^^^\nUnboundLocalError: cannot access local variable 'sftp' where it is not associated with a value"}
{"asctime": "2025-07-11 18:32:31,263599", "levelname": "ERROR", "name": "telgoo_reconciliation.py", "message": "Error extracting Zip file: [Errno 2] No such file or directory: 'ZIP/MVNO_Connect_BIlling_JUN2025.zip'", "job_id": "20250711_183116", "exception": "Traceback (most recent call last):\n  File \"/Users/<USER>/Desktop/mnovc_reconc_auto/src/telgoo_reconciliation.py\", line 344, in main\n    with zipfile.ZipFile(local_file, 'r') as zip_ref:\n         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/opt/anaconda3/lib/python3.12/zipfile/__init__.py\", line 1331, in __init__\n    self.fp = io.open(file, filemode)\n              ^^^^^^^^^^^^^^^^^^^^^^^\nFileNotFoundError: [Errno 2] No such file or directory: 'ZIP/MVNO_Connect_BIlling_JUN2025.zip'"}
{"asctime": "2025-07-11 18:32:31,266687", "levelname": "ERROR", "name": "telgoo_reconciliation.py", "message": "Error closing SFTP connection: cannot access local variable 'sftp' where it is not associated with a value", "job_id": "20250711_183116", "exception": "Traceback (most recent call last):\n  File \"/Users/<USER>/Desktop/mnovc_reconc_auto/src/telgoo_reconciliation.py\", line 350, in main\n    sftp.close()\n    ^^^^\nUnboundLocalError: cannot access local variable 'sftp' where it is not associated with a value"}
{"asctime": "2025-07-11 18:32:31,273666", "levelname": "ERROR", "name": "telgoo_reconciliation.py", "message": "Error loading input files: [Errno 2] No such file or directory: '/Users/<USER>/Desktop/mnovc_reconc_auto/MVNO_Connect_BIlling_IOT_JUN2025.csv'", "job_id": "20250711_183116", "exception": "Traceback (most recent call last):\n  File \"/Users/<USER>/Desktop/mnovc_reconc_auto/src/telgoo_reconciliation.py\", line 360, in main\n    telgoo_iot = pd.read_csv(telgoo_iot_file, dtype={'SUBSCRIBER_ID': str})\n                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/Desktop/mnovc_reconc_auto/.venv/lib/python3.12/site-packages/pandas/io/parsers/readers.py\", line 1026, in read_csv\n    return _read(filepath_or_buffer, kwds)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/Desktop/mnovc_reconc_auto/.venv/lib/python3.12/site-packages/pandas/io/parsers/readers.py\", line 620, in _read\n    parser = TextFileReader(filepath_or_buffer, **kwds)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/Desktop/mnovc_reconc_auto/.venv/lib/python3.12/site-packages/pandas/io/parsers/readers.py\", line 1620, in __init__\n    self._engine = self._make_engine(f, self.engine)\n                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/Desktop/mnovc_reconc_auto/.venv/lib/python3.12/site-packages/pandas/io/parsers/readers.py\", line 1880, in _make_engine\n    self.handles = get_handle(\n                   ^^^^^^^^^^^\n  File \"/Users/<USER>/Desktop/mnovc_reconc_auto/.venv/lib/python3.12/site-packages/pandas/io/common.py\", line 873, in get_handle\n    handle = open(\n             ^^^^^\nFileNotFoundError: [Errno 2] No such file or directory: '/Users/<USER>/Desktop/mnovc_reconc_auto/MVNO_Connect_BIlling_IOT_JUN2025.csv'"}
{"asctime": "2025-07-11 18:32:44,290136", "levelname": "INFO", "name": "snowflake.connector.connection", "message": "Snowflake Connector for Python Version: 3.15.0, Python Version: 3.12.7, Platform: macOS-15.5-arm64-arm-64bit", "job_id": null}
{"asctime": "2025-07-11 18:32:44,290961", "levelname": "INFO", "name": "snowflake.connector.connection", "message": "Connecting to GLOBAL Snowflake domain", "job_id": null}
{"asctime": "2025-07-11 18:32:33,051554", "levelname": "INFO", "name": "telgoo_reconciliation.py", "message": "Successfully connected to Snowflake.", "job_id": "20250711_183116"}
{"asctime": "2025-07-11 18:32:40,649523", "levelname": "INFO", "name": "telgoo_reconciliation.py", "message": "Retrieved IOT Snapshot data for dates 2025-06-02 to 2025-07-01. Total rows: 345634  ", "job_id": "20250711_183116"}
{"asctime": "2025-07-11 18:32:44,289741", "levelname": "INFO", "name": "telgoo_reconciliation.py", "message": "Retrieved NON-IOT Snapshot data for dates 2025-06-02 to 2025-07-01. Total rows: 348266  ", "job_id": "20250711_183116"}
{"asctime": "2025-07-11 18:32:44,289834", "levelname": "ERROR", "name": "telgoo_reconciliation.py", "message": "Error in Non-IoT Reconciliation': cannot access local variable 'telgoo_non_iot' where it is not associated with a value", "job_id": "20250711_183116", "exception": "Traceback (most recent call last):\n  File \"/Users/<USER>/Desktop/mnovc_reconc_auto/src/telgoo_reconciliation.py\", line 417, in main\n    telgoo_noniot_users, MSISDNWISE_non_iot, mvnoc_noniot_users= non_iot_recon(non_iot_MSISDN, telgoo_non_iot)\n                                                                                               ^^^^^^^^^^^^^^\nUnboundLocalError: cannot access local variable 'telgoo_non_iot' where it is not associated with a value"}
{"asctime": "2025-07-11 18:32:44,292584", "levelname": "ERROR", "name": "telgoo_reconciliation.py", "message": "Error in Non-Iot Reconciliation report genration.': 'NoneType' object has no attribute 'info'", "job_id": "20250711_183116", "exception": "Traceback (most recent call last):\n  File \"/Users/<USER>/Desktop/mnovc_reconc_auto/src/telgoo_reconciliation.py\", line 425, in main\n    report_path = make_non_iot_results_excel(\n                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/Desktop/mnovc_reconc_auto/src/telgoo_reconciliation.py\", line 141, in make_non_iot_results_excel\n    logger.info(f\"DEBUG: REPORT_DIR = {output_dir}\")\n    ^^^^^^^^^^^\nAttributeError: 'NoneType' object has no attribute 'info'"}
{"asctime": "2025-07-11 18:32:44,292870", "levelname": "ERROR", "name": "telgoo_reconciliation.py", "message": "Error in Iot Reconciliation': cannot access local variable 'telgoo_iot' where it is not associated with a value", "job_id": "20250711_183116", "exception": "Traceback (most recent call last):\n  File \"/Users/<USER>/Desktop/mnovc_reconc_auto/src/telgoo_reconciliation.py\", line 437, in main\n    MSISDNWISE_iot, mvnoc_billable_days, telgoo_billable_days= iot_recon(IOT_SNAPSHOT_BILLABLE, telgoo_iot)\n                                                                                                ^^^^^^^^^^\nUnboundLocalError: cannot access local variable 'telgoo_iot' where it is not associated with a value"}
{"asctime": "2025-07-11 18:32:44,293170", "levelname": "ERROR", "name": "telgoo_reconciliation.py", "message": "Error in Iot Reconciliation report genration.': 'NoneType' object has no attribute 'info'", "job_id": "20250711_183116", "exception": "Traceback (most recent call last):\n  File \"/Users/<USER>/Desktop/mnovc_reconc_auto/src/telgoo_reconciliation.py\", line 446, in main\n    report_path = make_iot_results_excel(\n                  ^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/Desktop/mnovc_reconc_auto/src/telgoo_reconciliation.py\", line 223, in make_iot_results_excel\n    logger.info(f\"DEBUG: REPORT_DIR = {output_dir}\")\n    ^^^^^^^^^^^\nAttributeError: 'NoneType' object has no attribute 'info'"}
{"asctime": "2025-07-11 18:32:44,902767", "levelname": "INFO", "name": "telgoo_reconciliation.py", "message": "Snowflake connection is closed.", "job_id": "20250711_183116"}
{"asctime": "2025-07-11 18:32:44,902987", "levelname": "INFO", "name": "telgoo_reconciliation.py", "message": "Telgoo reconciliation job ended at 2025-07-11 18:32:44.", "job_id": "20250711_183116"}
